from django.db import models
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from config.models import BaseModel
from core.models import SoftDeleteModel
from ..constants import SERVICE_TYPES, BILLING_CYCLES, SOLUTION_TYPES, TAB_TYPES, PROMOTION_TYPE_CHOICES
from decimal import Decimal
import logging

class ServicePromotion(SoftDeleteModel):
    code = models.CharField(max_length=50, unique=True)
    discount_percentage = models.IntegerField(
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    is_active = models.BooleanField(default=True)
    description = models.TextField(blank=True)
    stripe_promo_id = models.CharField(max_length=100, null=True, blank=True)
    promotion_type = models.CharField(
        max_length=10,
        choices=PROMOTION_TYPE_CHOICES,
        default="OFFLINE"
    )
    def __str__(self):
        return f"{self.code} ({self.discount_percentage}%)"

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['is_active']),
            models.Index(fields=['start_date', 'end_date']),
        ]

class Service(BaseModel):
    name = models.CharField(max_length=255)
    service_code = models.CharField(max_length=50, unique=True, null=True, blank=True)
    description = models.TextField()
    storage_size = models.CharField(max_length=50)  # e.g. "20GB", "15GB"
    features = models.JSONField()  # Store list of features
    price = models.DecimalField(max_digits=10, decimal_places=2)
    is_active = models.BooleanField(default=True)
    service_type = models.CharField(
        max_length=50,
        choices=SERVICE_TYPES
    )
    order = models.IntegerField(default=0)  # For sorting
    button_text = models.CharField(max_length=255, default="Get Started")
    
    # Promo codes
    active_promotions = models.ManyToManyField(
        ServicePromotion,
        related_name='services',
        blank=True
    )

    class Meta:
        ordering = ['order']

    def get_active_promotion(self):
        """Get the first active promotion if any exists"""
        from django.utils import timezone
        now = timezone.now()
        
        for promotion in self.active_promotions.filter(is_active=True, is_deleted=False):
            if promotion.start_date <= now <= promotion.end_date:
                return promotion
        return None

    def get_discounted_price(self):
        """Calculate the final price considering both service discount and active promotions"""
        from django.utils import timezone
        logger = logging.getLogger(__name__)
        
        now = timezone.now()
        logger.info(f"Calculating discounted price for service {self.id} at {now}")
        
        # Start with original price
        final_price = self.price
        logger.info(f"Original price: {final_price}")
        
        # Find the highest discount among active promotions
        max_promo_discount = Decimal('0')
        active_promotions = self.active_promotions.filter(
            is_active=True, 
            is_deleted=False,
            start_date__lte=now,
            end_date__gte=now
        )
        
        logger.info(f"Found {active_promotions.count()} active promotions")
        for promotion in active_promotions:
            if promotion.discount_percentage is None:
                continue
            try:
                promo_discount = Decimal(str(promotion.discount_percentage)) / Decimal('100')
            except Exception:
                continue
            logger.info(f"Promotion {promotion.code}: {promotion.discount_percentage}% discount")
            if promo_discount > max_promo_discount:
                max_promo_discount = promo_discount
        
        # Apply only the highest promotion discount if any
        if max_promo_discount > 0:
            final_price = final_price * (Decimal('1') - max_promo_discount)
            logger.info(f"After highest promotion discount ({max_promo_discount*100}%): {final_price}")
        
        # Round to 2 decimal places
        final_price = final_price.quantize(Decimal('0.01'))
        logger.info(f"Final rounded price: {final_price}")
        return final_price

    def is_discount_active(self):
        """Check if either service discount or any promotion is active"""
        from django.utils import timezone
        now = timezone.now()
        
        # Check active promotions
        active_promotions = self.active_promotions.filter(
            is_active=True,
            is_deleted=False,
            start_date__lte=now,
            end_date__gte=now
        )
        return active_promotions.exists()

    def get_best_discounted_price(self, promo_code=None):
        """
        Calculate the best discounted price for the service, considering:
        - Active online promotions (auto-applied)
        - Offline promotion (if promo_code provided)
        Returns: (final_price, applied_promotion: ServicePromotion|None, applied_type: str)
        """
        from django.utils import timezone
        now = timezone.now()
        from decimal import Decimal
        best_discount = Decimal('0')
        applied_promotion = None
        applied_type = None
        
        # 1. Active online promotions (auto-applied)
        online_promos = self.active_promotions.filter(
            is_active=True,
            is_deleted=False,
            promotion_type='ONLINE',
            start_date__lte=now,
            end_date__gte=now
        )
        for promo in online_promos:
            if promo.discount_percentage is None:
                continue
            try:
                promo_discount = Decimal(str(promo.discount_percentage)) / Decimal('100')
            except Exception:
                continue
            if promo_discount > best_discount:
                best_discount = promo_discount
                applied_promotion = promo
                applied_type = 'PROMO_ONLINE'
        
        # 2. Offline promotion (user input code)
        if promo_code:
            offline_promo = self.active_promotions.filter(
                is_active=True,
                is_deleted=False,
                promotion_type='OFFLINE',
                code=promo_code,
                start_date__lte=now,
                end_date__gte=now
            ).first()
            if offline_promo:
                if offline_promo.discount_percentage is not None:
                    try:
                        promo_discount = Decimal(str(offline_promo.discount_percentage)) / Decimal('100')
                    except Exception:
                        promo_discount = Decimal('0')
                    if promo_discount > best_discount:
                        best_discount = promo_discount
                        applied_promotion = offline_promo
                        applied_type = 'PROMO_OFFLINE'
        
        # 3. Calculate final price
        final_price = self.price * (Decimal('1') - best_discount)
        final_price = final_price.quantize(Decimal('0.01'))
        
        return final_price, applied_promotion, applied_type

class SubscriptionPlan(BaseModel):
    name = models.CharField(max_length=255)
    description = models.TextField()
    features = models.JSONField()  # Store list of features
    price = models.DecimalField(max_digits=10, decimal_places=2)
    billing_cycle = models.CharField(
        max_length=50,
        choices=BILLING_CYCLES
    )
    button_text = models.CharField(max_length=255, default="Get Started")
    is_active = models.BooleanField(default=True)
    order = models.IntegerField(default=0)
    
    # Promo codes
    active_promotions = models.ManyToManyField(
        ServicePromotion,
        related_name='subscription_plans',
        blank=True
    )

    class Meta:
        ordering = ['order']

    def get_active_promotion(self):
        """Get the first active promotion if any exists"""
        from django.utils import timezone
        now = timezone.now()
        
        for promotion in self.active_promotions.filter(is_active=True, is_deleted=False):
            if promotion.start_date <= now <= promotion.end_date:
                return promotion
        return None

    def get_discounted_price(self):
        """Calculate the final price considering both plan discount and active promotions"""
        from django.utils import timezone
        logger = logging.getLogger(__name__)
        
        now = timezone.now()
        logger.info(f"Calculating discounted price for subscription plan {self.id} at {now}")
        
        # Start with original price
        final_price = self.price
        logger.info(f"Original price: {final_price}")
        
        # Find the highest discount among active promotions
        max_promo_discount = Decimal('0')
        active_promotions = self.active_promotions.filter(
            is_active=True, 
            is_deleted=False,
            start_date__lte=now,
            end_date__gte=now
        )
        
        logger.info(f"Found {active_promotions.count()} active promotions")
        for promotion in active_promotions:
            if promotion.discount_percentage is None:
                continue
            try:
                promo_discount = Decimal(str(promotion.discount_percentage)) / Decimal('100')
            except Exception:
                continue
            logger.info(f"Promotion {promotion.code}: {promotion.discount_percentage}% discount")
            if promo_discount > max_promo_discount:
                max_promo_discount = promo_discount
        
        # Apply only the highest promotion discount if any
        if max_promo_discount > 0:
            final_price = final_price * (Decimal('1') - max_promo_discount)
            logger.info(f"After highest promotion discount ({max_promo_discount*100}%): {final_price}")
        
        # Round to 2 decimal places
        final_price = final_price.quantize(Decimal('0.01'))
        logger.info(f"Final rounded price: {final_price}")
        return final_price

    def is_discount_active(self):
        """Check if either plan discount or any promotion is active"""
        from django.utils import timezone
        now = timezone.now()
        
        # Check active promotions
        active_promotions = self.active_promotions.filter(
            is_active=True,
            is_deleted=False,
            start_date__lte=now,
            end_date__gte=now
        )
        return active_promotions.exists()

    def get_best_discounted_price(self, promo_code=None):
        """
        Calculate the best discounted price for the subscription plan, considering:
        - Active online promotions (auto-applied)
        - Offline promotion (if promo_code provided)
        Returns: (final_price, applied_promotion: ServicePromotion|None, applied_type: str)
        """
        from django.utils import timezone
        now = timezone.now()
        from decimal import Decimal
        best_discount = Decimal('0')
        applied_promotion = None
        applied_type = None
        # 1. Active online promotions (auto-applied)
        online_promos = self.active_promotions.filter(
            is_active=True,
            is_deleted=False,
            promotion_type='ONLINE',
            start_date__lte=now,
            end_date__gte=now
        )
        for promo in online_promos:
            if promo.discount_percentage is None:
                continue
            try:
                promo_discount = Decimal(str(promo.discount_percentage)) / Decimal('100')
            except Exception:
                continue
            if promo_discount > best_discount:
                best_discount = promo_discount
                applied_promotion = promo
                applied_type = 'PROMO_ONLINE'
        # 2. Offline promotion (user input code)
        if promo_code:
            offline_promo = self.active_promotions.filter(
                is_active=True,
                is_deleted=False,
                promotion_type='OFFLINE',
                code=promo_code,
                start_date__lte=now,
                end_date__gte=now
            ).first()
            if offline_promo:
                if offline_promo.discount_percentage is not None:
                    try:
                        promo_discount = Decimal(str(offline_promo.discount_percentage)) / Decimal('100')
                    except Exception:
                        promo_discount = Decimal('0')
                    if promo_discount > best_discount:
                        best_discount = promo_discount
                        applied_promotion = offline_promo
                        applied_type = 'PROMO_OFFLINE'
        # 3. Tính giá cuối cùng
        final_price = self.price * (Decimal('1') - best_discount)
        final_price = final_price.quantize(Decimal('0.01'))
        return final_price, applied_promotion, applied_type

class Solution(BaseModel):
    name = models.CharField(max_length=255)
    description = models.TextField()
    solution_type = models.CharField(
        max_length=50,
        choices=SOLUTION_TYPES
    )
    features = models.JSONField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    seat_price = models.DecimalField(max_digits=10, decimal_places=2, default=0.00, help_text="Price per seat in USD")
    is_active = models.BooleanField(default=True)
    order = models.IntegerField(default=0)
    button_text = models.CharField(max_length=255, default="Get Started")
    class Meta:
        ordering = ['order']

class TabAlert(BaseModel):
    message = models.TextField()
    tab = models.CharField(
        max_length=50,
        choices=TAB_TYPES
    )
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    is_active = models.BooleanField(default=True)

    class Meta:
        ordering = ['-created_at']

class InvitationCode(BaseModel):
    """
    Model for managing invitation codes required for user registration
    """
    code = models.CharField(max_length=50, unique=True, help_text="Unique invitation code")
    description = models.TextField(blank=True, help_text="Description or purpose of this invitation code")
    max_uses = models.PositiveIntegerField(default=1, help_text="Maximum number of times this code can be used")
    current_uses = models.PositiveIntegerField(default=0, help_text="Current number of times this code has been used")
    is_active = models.BooleanField(default=True, help_text="Whether this invitation code is active")
    expires_at = models.DateTimeField(null=True, blank=True, help_text="When this invitation code expires")
    created_by = models.ForeignKey(
        'accounts.CustomUser', 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='created_invitation_codes',
        help_text="User who created this invitation code"
    )
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['is_active']),
            models.Index(fields=['expires_at']),
        ]
    
    def __str__(self):
        return f"Invitation Code: {self.code} ({self.current_uses}/{self.max_uses})"
    
    def is_valid(self):
        """Check if invitation code is valid for use"""
        from django.utils import timezone
        
        # Check if active
        if not self.is_active:
            return False, "Invitation code is not active"
        
        # Check if expired
        if self.expires_at and timezone.now() > self.expires_at:
            return False, "Invitation code has expired"
        
        # Check if max uses exceeded
        if self.current_uses >= self.max_uses:
            return False, "Invitation code has reached maximum uses"
        
        return True, "Valid"
    
    def use_code(self):
        """Increment the usage count for this invitation code"""
        if self.current_uses < self.max_uses:
            self.current_uses += 1
            self.save(update_fields=['current_uses', 'updated_at'])
            return True
        return False 